// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// AutoRouterGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:auto_route/auto_route.dart' as _i20;
import 'package:flutter/material.dart' as _i21;
import 'package:storetrack_app/features/auth/presentation/pages/login_page.dart'
    as _i6;
import 'package:storetrack_app/features/auth/presentation/pages/reset_password_page.dart'
    as _i12;
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart'
    as _i22;
import 'package:storetrack_app/features/home/<USER>/pages/assistant_page.dart'
    as _i1;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_holder_page.dart'
    as _i2;
import 'package:storetrack_app/features/home/<USER>/pages/dashboard_page.dart'
    as _i3;
import 'package:storetrack_app/features/home/<USER>/pages/home_page.dart'
    as _i4;
import 'package:storetrack_app/features/home/<USER>/pages/journey_map_page.dart'
    as _i5;
import 'package:storetrack_app/features/home/<USER>/pages/more_page.dart'
    as _i7;
import 'package:storetrack_app/features/home/<USER>/pages/notes_page.dart'
    as _i8;
import 'package:storetrack_app/features/home/<USER>/pages/pos_page.dart'
    as _i10;
import 'package:storetrack_app/features/home/<USER>/pages/profile_page.dart'
    as _i11;
import 'package:storetrack_app/features/home/<USER>/pages/scheduled_page.dart'
    as _i13;
import 'package:storetrack_app/features/home/<USER>/pages/store_history_page.dart'
    as _i15;
import 'package:storetrack_app/features/home/<USER>/pages/task_details_page.dart'
    as _i16;
import 'package:storetrack_app/features/home/<USER>/pages/todays_page.dart'
    as _i17;
import 'package:storetrack_app/features/home/<USER>/pages/unscheduled_page.dart'
    as _i18;
import 'package:storetrack_app/features/notification/presentation/pages/notification_page.dart'
    as _i9;
import 'package:storetrack_app/features/splash/presentation/pages/splash_page.dart'
    as _i14;
import 'package:storetrack_app/features/web_browser/presentation/pages/web_browser_page.dart'
    as _i19;

/// generated route for
/// [_i1.AssistantPage]
class AssistantRoute extends _i20.PageRouteInfo<void> {
  const AssistantRoute({List<_i20.PageRouteInfo>? children})
      : super(
          AssistantRoute.name,
          initialChildren: children,
        );

  static const String name = 'AssistantRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i1.AssistantPage();
    },
  );
}

/// generated route for
/// [_i2.DashboardHolderPage]
class DashboardHolderRoute extends _i20.PageRouteInfo<void> {
  const DashboardHolderRoute({List<_i20.PageRouteInfo>? children})
      : super(
          DashboardHolderRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardHolderRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i2.DashboardHolderPage();
    },
  );
}

/// generated route for
/// [_i3.DashboardPage]
class DashboardRoute extends _i20.PageRouteInfo<void> {
  const DashboardRoute({List<_i20.PageRouteInfo>? children})
      : super(
          DashboardRoute.name,
          initialChildren: children,
        );

  static const String name = 'DashboardRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i3.DashboardPage();
    },
  );
}

/// generated route for
/// [_i4.HomePage]
class HomeRoute extends _i20.PageRouteInfo<void> {
  const HomeRoute({List<_i20.PageRouteInfo>? children})
      : super(
          HomeRoute.name,
          initialChildren: children,
        );

  static const String name = 'HomeRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i4.HomePage();
    },
  );
}

/// generated route for
/// [_i5.JourneyMapPage]
class JourneyMapRoute extends _i20.PageRouteInfo<void> {
  const JourneyMapRoute({List<_i20.PageRouteInfo>? children})
      : super(
          JourneyMapRoute.name,
          initialChildren: children,
        );

  static const String name = 'JourneyMapRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i5.JourneyMapPage();
    },
  );
}

/// generated route for
/// [_i6.LoginPage]
class LoginRoute extends _i20.PageRouteInfo<void> {
  const LoginRoute({List<_i20.PageRouteInfo>? children})
      : super(
          LoginRoute.name,
          initialChildren: children,
        );

  static const String name = 'LoginRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i6.LoginPage();
    },
  );
}

/// generated route for
/// [_i7.MorePage]
class MoreRoute extends _i20.PageRouteInfo<void> {
  const MoreRoute({List<_i20.PageRouteInfo>? children})
      : super(
          MoreRoute.name,
          initialChildren: children,
        );

  static const String name = 'MoreRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i7.MorePage();
    },
  );
}

/// generated route for
/// [_i8.NotesPage]
class NotesRoute extends _i20.PageRouteInfo<NotesRouteArgs> {
  NotesRoute({
    _i21.Key? key,
    required _i22.TaskDetail task,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          NotesRoute.name,
          args: NotesRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'NotesRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<NotesRouteArgs>();
      return _i8.NotesPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class NotesRouteArgs {
  const NotesRouteArgs({
    this.key,
    required this.task,
  });

  final _i21.Key? key;

  final _i22.TaskDetail task;

  @override
  String toString() {
    return 'NotesRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i9.NotificationsPage]
class NotificationsRoute extends _i20.PageRouteInfo<void> {
  const NotificationsRoute({List<_i20.PageRouteInfo>? children})
      : super(
          NotificationsRoute.name,
          initialChildren: children,
        );

  static const String name = 'NotificationsRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i9.NotificationsPage();
    },
  );
}

/// generated route for
/// [_i10.PosPage]
class PosRoute extends _i20.PageRouteInfo<PosRouteArgs> {
  PosRoute({
    _i21.Key? key,
    _i22.TaskDetail? task,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          PosRoute.name,
          args: PosRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'PosRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args =
          data.argsAs<PosRouteArgs>(orElse: () => const PosRouteArgs());
      return _i10.PosPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class PosRouteArgs {
  const PosRouteArgs({
    this.key,
    this.task,
  });

  final _i21.Key? key;

  final _i22.TaskDetail? task;

  @override
  String toString() {
    return 'PosRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i11.ProfilePage]
class ProfileRoute extends _i20.PageRouteInfo<void> {
  const ProfileRoute({List<_i20.PageRouteInfo>? children})
      : super(
          ProfileRoute.name,
          initialChildren: children,
        );

  static const String name = 'ProfileRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i11.ProfilePage();
    },
  );
}

/// generated route for
/// [_i12.ResetPasswordPage]
class ResetPasswordRoute extends _i20.PageRouteInfo<ResetPasswordRouteArgs> {
  ResetPasswordRoute({
    _i21.Key? key,
    required String email,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          ResetPasswordRoute.name,
          args: ResetPasswordRouteArgs(
            key: key,
            email: email,
          ),
          initialChildren: children,
        );

  static const String name = 'ResetPasswordRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<ResetPasswordRouteArgs>();
      return _i12.ResetPasswordPage(
        key: args.key,
        email: args.email,
      );
    },
  );
}

class ResetPasswordRouteArgs {
  const ResetPasswordRouteArgs({
    this.key,
    required this.email,
  });

  final _i21.Key? key;

  final String email;

  @override
  String toString() {
    return 'ResetPasswordRouteArgs{key: $key, email: $email}';
  }
}

/// generated route for
/// [_i13.SchedulePage]
class ScheduleRoute extends _i20.PageRouteInfo<void> {
  const ScheduleRoute({List<_i20.PageRouteInfo>? children})
      : super(
          ScheduleRoute.name,
          initialChildren: children,
        );

  static const String name = 'ScheduleRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i13.SchedulePage();
    },
  );
}

/// generated route for
/// [_i14.SplashPage]
class SplashRoute extends _i20.PageRouteInfo<void> {
  const SplashRoute({List<_i20.PageRouteInfo>? children})
      : super(
          SplashRoute.name,
          initialChildren: children,
        );

  static const String name = 'SplashRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i14.SplashPage();
    },
  );
}

/// generated route for
/// [_i15.StoreHistoryPage]
class StoreHistoryRoute extends _i20.PageRouteInfo<StoreHistoryRouteArgs> {
  StoreHistoryRoute({
    _i21.Key? key,
    required int storeId,
    required int taskId,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          StoreHistoryRoute.name,
          args: StoreHistoryRouteArgs(
            key: key,
            storeId: storeId,
            taskId: taskId,
          ),
          initialChildren: children,
        );

  static const String name = 'StoreHistoryRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<StoreHistoryRouteArgs>();
      return _i15.StoreHistoryPage(
        key: args.key,
        storeId: args.storeId,
        taskId: args.taskId,
      );
    },
  );
}

class StoreHistoryRouteArgs {
  const StoreHistoryRouteArgs({
    this.key,
    required this.storeId,
    required this.taskId,
  });

  final _i21.Key? key;

  final int storeId;

  final int taskId;

  @override
  String toString() {
    return 'StoreHistoryRouteArgs{key: $key, storeId: $storeId, taskId: $taskId}';
  }
}

/// generated route for
/// [_i16.TaskDetailsPage]
class TaskDetailsRoute extends _i20.PageRouteInfo<TaskDetailsRouteArgs> {
  TaskDetailsRoute({
    _i21.Key? key,
    required _i22.TaskDetail task,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          TaskDetailsRoute.name,
          args: TaskDetailsRouteArgs(
            key: key,
            task: task,
          ),
          initialChildren: children,
        );

  static const String name = 'TaskDetailsRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<TaskDetailsRouteArgs>();
      return _i16.TaskDetailsPage(
        key: args.key,
        task: args.task,
      );
    },
  );
}

class TaskDetailsRouteArgs {
  const TaskDetailsRouteArgs({
    this.key,
    required this.task,
  });

  final _i21.Key? key;

  final _i22.TaskDetail task;

  @override
  String toString() {
    return 'TaskDetailsRouteArgs{key: $key, task: $task}';
  }
}

/// generated route for
/// [_i17.TodayPage]
class TodayRoute extends _i20.PageRouteInfo<void> {
  const TodayRoute({List<_i20.PageRouteInfo>? children})
      : super(
          TodayRoute.name,
          initialChildren: children,
        );

  static const String name = 'TodayRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i17.TodayPage();
    },
  );
}

/// generated route for
/// [_i18.UnscheduledPage]
class UnscheduledRoute extends _i20.PageRouteInfo<void> {
  const UnscheduledRoute({List<_i20.PageRouteInfo>? children})
      : super(
          UnscheduledRoute.name,
          initialChildren: children,
        );

  static const String name = 'UnscheduledRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      return const _i18.UnscheduledPage();
    },
  );
}

/// generated route for
/// [_i19.WebBrowserPage]
class WebBrowserRoute extends _i20.PageRouteInfo<WebBrowserRouteArgs> {
  WebBrowserRoute({
    _i21.Key? key,
    required String url,
    List<_i20.PageRouteInfo>? children,
  }) : super(
          WebBrowserRoute.name,
          args: WebBrowserRouteArgs(
            key: key,
            url: url,
          ),
          initialChildren: children,
        );

  static const String name = 'WebBrowserRoute';

  static _i20.PageInfo page = _i20.PageInfo(
    name,
    builder: (data) {
      final args = data.argsAs<WebBrowserRouteArgs>();
      return _i19.WebBrowserPage(
        key: args.key,
        url: args.url,
      );
    },
  );
}

class WebBrowserRouteArgs {
  const WebBrowserRouteArgs({
    this.key,
    required this.url,
  });

  final _i21.Key? key;

  final String url;

  @override
  String toString() {
    return 'WebBrowserRouteArgs{key: $key, url: $url}';
  }
}
